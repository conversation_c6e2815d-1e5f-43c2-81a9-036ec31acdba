# Food Eazy 🍕

A modern, full-stack food ordering platform built with React and Node.js. Features optional integrations for email notifications, cloud storage, and online payments.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-v14+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-v16+-blue.svg)](https://reactjs.org/)

## 🚀 Features

### User Features
- **Authentication System**: Secure user registration and login
- **Email Verification**: Optional 2-factor authentication via email
- **Password Recovery**: Secure password reset functionality
- **Food Browsing**: Search and sort food items by name or price
- **Shopping Cart**: Save, modify, and manage cart items
- **Order Management**: Choose delivery or takeaway options
- **Payment Options**: Cash payments with optional online payment integration
- **Order History**: View current and previous orders

### Admin Features
- **Product Management**: Add and remove food items
- **Image Upload**: Support for both local and cloud storage
- **Order Monitoring**: Track all customer orders

### Optional Integrations
- **📧 Email Service**: SendGrid integration for notifications
- **☁️ Cloud Storage**: Cloudinary for image management
- **💳 Online Payments**: PayTM integration for digital transactions

## 🛠️ Tech Stack

**Frontend:**
- React.js
- Material-UI
- React Router
- Axios

**Backend:**
- Node.js
- Express.js
- MongoDB with Mongoose
- JWT Authentication

**Optional Services:**
- SendGrid (Email)
- Cloudinary (Image Storage)
- PayTM (Payments)
## 📦 Installation

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local or cloud instance)
- npm or yarn package manager

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <your-repository-url>
   cd foodeazy
   ```

2. **Install dependencies**
   ```bash
   npm install
   npm run install-client
   ```

3. **Environment Configuration**

   Create a `.env` file in the root directory with the following variables:

   ```env
   # MongoDB Configuration
   MONGO_URI=mongodb://localhost:27017/foodeazy

   # JWT Secret
   JWT_SECRET=your_jwt_secret_key_here

   # Server Port
   PORT=5000

   # Feature Toggles - Set to 'true' to enable, 'false' to disable
   ENABLE_EMAIL=false
   ENABLE_CLOUDINARY=false
   ENABLE_PAYTM=false

   # Email Configuration (SendGrid) - Only used if ENABLE_EMAIL=true
   SEND_GRID_KEY=your_sendgrid_api_key_here
   EMAIL_FROM=<EMAIL>

   # PayTM Configuration - Only used if ENABLE_PAYTM=true
   MERCHANT_ID=your_merchant_id_here
   MERCHANT_API_KEY=your_merchant_api_key_here
   PAYTM_CALLBACK_URL=http://localhost:3000

   # Cloudinary Configuration - Only used if ENABLE_CLOUDINARY=true
   CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
   CLOUDINARY_API_KEY=your_cloudinary_api_key
   CLOUDINARY_API_SECRET=your_cloudinary_api_secret
   ```

4. **Start the application**
   ```bash
   npm run dev
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## ⚙️ Configuration Options

### Feature Toggles

This application supports optional features that can be enabled or disabled via environment variables:

#### Email Service (ENABLE_EMAIL)
- **Enabled**: Users receive email verification, password reset, and order confirmation emails
- **Disabled**: Application works without email functionality, users can still register and order

#### Cloud Storage (ENABLE_CLOUDINARY)
- **Enabled**: Product images are uploaded to Cloudinary
- **Disabled**: Images are stored locally in the `/images` directory

#### Online Payments (ENABLE_PAYTM)
- **Enabled**: Users can pay online via PayTM
- **Disabled**: Only cash payment option is available

### Database Setup

1. **Local MongoDB**
   ```bash
   # Install MongoDB locally or use Docker
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

2. **MongoDB Atlas (Cloud)**
   - Create account at [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
   - Create cluster and get connection string
   - Update `MONGO_URI` in `.env` file

## 🚀 Deployment

### Environment-Specific Configuration

For production deployment, ensure all required environment variables are set:

```bash
# Production example
MONGO_URI=mongodb+srv://username:<EMAIL>/foodeazy
JWT_SECRET=your_production_jwt_secret
ENABLE_EMAIL=true
ENABLE_CLOUDINARY=true
ENABLE_PAYTM=true
# ... other production configs
```

### Build for Production

```bash
npm run build
npm start
```

## 📝 API Documentation

### Authentication Endpoints
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/verify` - Email verification

### Product Endpoints
- `GET /api/items` - Get all products
- `POST /api/admin/upload` - Add new product (Admin only)
- `DELETE /api/admin/items/:id` - Delete product (Admin only)

### Order Endpoints
- `POST /api/orders` - Place order (Cash payment)
- `POST /api/paytm` - Place order (Online payment)
- `GET /api/get_orders` - Get user orders

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [Issues](../../issues) section
2. Create a new issue with detailed information
3. Contact <NAME_EMAIL>

---

**Made with ❤️ for the food community**


