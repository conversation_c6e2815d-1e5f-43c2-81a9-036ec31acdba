import React from "react";
import { makeStyles } from "@material-ui/core/styles";
import { Card } from "@material-ui/core";
import { CardContent } from "@material-ui/core/";
import { Typography } from "@material-ui/core";
import FavoriteIcon from "@material-ui/icons/Favorite";
import EmailIcon from "@material-ui/icons/Email";
import PhoneIcon from "@material-ui/icons/Phone";
import LocationOnIcon from "@material-ui/icons/LocationOn";
import "animate.css";

const useStyles = makeStyles({
  root: {
    maxWidth: 600,
    margin: "auto",
    marginTop: "2rem",
    padding: "2rem"
  },
  contactInfo: {
    display: "flex",
    alignItems: "center",
    marginBottom: "1rem",
    "& svg": {
      marginRight: "1rem",
      color: "#ff6b35"
    }
  }
});

const Contact = () => {
  const classes = useStyles();
  return (
    <>
      <h1
        style={{ fontFamily: "Alata", paddingTop: "1rem", textAlign: "center" }}
        className="animate__animated animate__fadeIn"
      >
        Contact <FavoriteIcon style={{ color: "red" }} /> Food Eazy
      </h1>

      <div className="contact_container">
        <Card className={classes.root} variant="outlined">
          <CardContent>
            <Typography
              gutterBottom
              variant="h4"
              component="h2"
              style={{ fontFamily: "Mulish", textAlign: "center", marginBottom: "2rem" }}
            >
              Get in Touch
            </Typography>

            <Typography
              variant="body1"
              style={{ fontFamily: "Poppins", marginBottom: "2rem", textAlign: "center" }}
            >
              We'd love to hear from you! Reach out to us for any questions, feedback, or support.
            </Typography>

            <div className={classes.contactInfo}>
              <EmailIcon />
              <Typography variant="body1" style={{ fontFamily: "Poppins" }}>
                <EMAIL>
              </Typography>
            </div>

            <div className={classes.contactInfo}>
              <PhoneIcon />
              <Typography variant="body1" style={{ fontFamily: "Poppins" }}>
                +****************
              </Typography>
            </div>

            <div className={classes.contactInfo}>
              <LocationOnIcon />
              <Typography variant="body1" style={{ fontFamily: "Poppins" }}>
                123 Food Street, Culinary City, FC 12345
              </Typography>
            </div>

            <Typography
              variant="h6"
              style={{ fontFamily: "Mulish", marginTop: "2rem", marginBottom: "1rem" }}
            >
              Business Hours
            </Typography>

            <Typography variant="body2" style={{ fontFamily: "Poppins" }}>
              Monday - Friday: 9:00 AM - 10:00 PM<br />
              Saturday - Sunday: 10:00 AM - 11:00 PM
            </Typography>

            <Typography
              variant="body2"
              style={{ fontFamily: "Poppins", marginTop: "2rem", textAlign: "center", fontStyle: "italic" }}
            >
              Thank you for choosing Food Eazy - Making food ordering easy and convenient!
            </Typography>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default Contact;
