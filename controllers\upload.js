const multer = require("multer");
const path = require("path");

// Configure storage based on whether Cloudinary is enabled
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        // Always save to local images folder (for backup or when Cloudinary is disabled)
        cb(null, './images/');
    },
    filename: function (req, file, cb) {
        // Generate unique filename
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'productImage-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        files: 100,
        fileSize: 1 * 512 * 1024
    },
    fileFilter: function (req, file, callback) {
        var ext = path.extname(file.originalname);
        if (ext !== '.png' && ext !== '.jpg' && ext !== '.gif' && ext !== '.jpeg') {
            return callback(new Error('Only images are allowed'))
        }
        callback(null, true)
    }
}).single("productImage")

module.exports = upload;