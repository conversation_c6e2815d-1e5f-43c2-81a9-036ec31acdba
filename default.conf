server {
        listen 80;
        listen [::]:80;

        root /var/www/html/;
        server_name your-domain.com www.your-domain.com;
        index index.html index.htm index.nginx-debian.html;

        server_name localhost;

        location / {
                proxy_pass http://mainapp:5000;
        }

        # location ~ /.well-known/acme-challenge {
                location ~ /\.well-known/acme-challenge {
                allow all;
                # try_files $uri =404;
                root /var/www/html/;
        }
}