{"name": "foodeazy", "version": "1.0.0", "description": "A modern food ordering platform with optional email, payment, and cloud storage features", "main": "server.js", "scripts": {"start": "node server.js", "build": "cd client && npm run build", "install-client": "cd client && npm install", "client": "cd client && npm start", "dev": "concurrently \"node server.js\" \"npm run client\"", "heroku-postbuild": "npm run install-client and npm run build"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "1.19.0", "cloudinary": "^1.22.0", "config": "3.3.1", "cors": "^2.8.5", "cron": "^1.8.2", "crypto": "^1.0.1", "dotenv": "^8.2.0", "express": "4.17.1", "express-validator": "^6.6.0", "jsonwebtoken": "^8.5.1", "mongoose": "5.9.25", "morgan": "^1.10.0", "multer": "^1.4.2", "nodemailer": "^6.4.10", "nodemailer-sendgrid-transport": "^0.2.0", "query-string": "^6.13.1", "querystring": "^0.2.0", "shortid": "^2.2.15", "uuid": "^8.3.0"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "1.18.4"}, "keywords": []}