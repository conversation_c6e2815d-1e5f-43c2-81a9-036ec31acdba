const express = require("express");
const upload = require("../controllers/upload");
const Router = express.Router();
const Product = require("../models/product");
const User = require("../models/register");
const Cloudinary = require("cloudinary");
const auth = require("../middleware/auth");
const config = require("config");
const path = require("path");

// Cloudinary configuration - only if enabled
let cloudinaryEnabled = false;
if (process.env.ENABLE_CLOUDINARY === 'true' && process.env.CLOUDINARY_CLOUD_NAME) {
  Cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
  });
  cloudinaryEnabled = true;
}

Router.post("/upload", auth, async (req, res) => {
  upload(req, res, async (err) => {
    if (err) {
      res.status(400).json(err);
    } else {
      let user = await User.findOne({ _id: req.user.id });
      if (user.role !== "admin")
        return res
          .status(401)
          .json(
            "Your request was processed but only admins are allowed to add or remove items!"
          );
      let product = await Product.findOne({
        name: req.body.name,
      });

      if (product) return res.status(400).json("Product already exists!");

      if (cloudinaryEnabled) {
        // Upload to Cloudinary
        Cloudinary.v2.uploader.upload(req.file.path, async function (
          error,
          result
        ) {
          if (error) {
            console.log("Cloudinary error:", error);
            return res.status(500).json("Image upload failed");
          }

          product = new Product({
            type: req.body.type,
            name: req.body.name,
            price: req.body.price,
            url: result.url,
          });
          try {
            await product.save();
            return res.status(200).json("Item Saved Successfully!");
          } catch (e) {
            console.log(e);
            res.status(500).send("Server Error");
          }
        });
      } else {
        // Use local file storage
        const imageUrl = `/images/${req.file.filename}`;
        product = new Product({
          type: req.body.type,
          name: req.body.name,
          price: req.body.price,
          url: imageUrl,
        });
        try {
          await product.save();
          return res.status(200).json("Item Saved Successfully!");
        } catch (e) {
          console.log(e);
          res.status(500).send("Server Error");
        }
      }
    }
  });
});

Router.get("/items", async (req, res) => {
  Product.find({}, (err, items) => {
    if (err) res.status(400).json(err);
    res.status(200).json(items);
  });
});

Router.post("/items", auth, async (req, res) => {
  try {
    let user = await User.findOne({ _id: req.user.id });
    if (user.role !== "admin")
      return res
        .status(401)
        .json(
          "Your request was processed but only admins are allowed to add or remove items!"
        );
    const item = await Product.deleteOne({
      name: req.body.name,
    });
    res.status(200).send(item);
  } catch (e) {
    console.log(e);
    res.sendStatus(500);
  }
});
module.exports = Router;
