const express = require("express");
const router = express.Router();
const Forget = require('../models/forget')
const User = require("../models/register");
const nodemailer = require('nodemailer');
const sendgridTransport = require('nodemailer-sendgrid-transport');
const shortid = require('shortid');

// Email transporter - only initialize if email is enabled
let transporter = null;
if (process.env.ENABLE_EMAIL === 'true' && process.env.SEND_GRID_KEY) {
  transporter = nodemailer.createTransport(
    sendgridTransport({
      auth: {
        api_key: process.env.SEND_GRID_KEY
      }
    })
  );
}


router.post("/", async(req, res)=>{
	let {email} = req.body;
	User.findOne({email}, async(err, user)=>{
		if(err)
		{
			console.log(err);
			res.status(400).json(err);
		}
		else if(!user)
		{
			console.log(err);
			res.status(400).json("User not registered!");
		}
		else
		{
			let code = shortid.generate();
		  const baseUrl = process.env.BASE_URL || "http://localhost:3000";
		  let link = `${baseUrl}/forget/${code}`;
		  Forget.findOneAndDelete({email}, (err)=>{
		  	if(err)
		  		console.log(err);
		  })
		  forget = new Forget({
		  	email,
		  	code
		  })
		  await forget.save();
		  let name = user.name;

		  // Send email only if enabled
		  if (transporter) {
		    var mailOptions = {
		          from: process.env.EMAIL_FROM || "<EMAIL>",
		          to: `${email}`	,
		          subject: "Reset your password for Food Eazy",
		          text: "That was easy!",
		          html: "Hi, <strong>" + name+"</strong><p>Follow this <a href=" + link+ " >Link</a> to reset your passowrd.</p><p>If this request is not made by you kindly ignore this mail.</p><p>Regards, <strong>Food Eazy</strong></p>",
		        };
		      transporter.sendMail(mailOptions, function (error, info) {
		          if (error) {
		            console.log("Email error:", error);
		            res.status(200).json('Password reset code generated. Email service unavailable.');
		          } else {
		            res.status(200).json('Reset Email Sent to your mail id');
		          }
		      });
		  } else {
		    console.log("Email disabled - password reset code generated but not sent");
		    res.status(200).json('Password reset code generated. Please contact support for assistance.');
		  }


		}
	})
})


module.exports = router;